import { get } from "./get";

console.log("TODO: Implement me!");

// Define the types based on the API structure
interface Workplace {
  id: number;
  name: string;
  status: number;
}

interface PaginatedResponse<T> {
  data: T[];
  links: { next: string | null };
}

async function getAllWorkplaces(): Promise<Map<number, Workplace>> {
  const workplacesMap = new Map<number, Workplace>();
  let nextUrl: string | null = "http://localhost:3000/workplaces?page=1";

  while (nextUrl) {
    console.log(`Fetching: ${nextUrl}`);
    const response: PaginatedResponse<Workplace> = await get(nextUrl);

    // Add each workplace to the map with ID as key
    response.data.forEach((workplace) => {
      workplacesMap.set(workplace.id, workplace);
    });

    // Update nextUrl for the next iteration
    nextUrl = response.links.next;
  }

  console.log(`Fetched ${workplacesMap.size} workplaces total`);
  return workplacesMap;
}

export async function main() {
  const workplacesMap = await getAllWorkplaces();
  console.log("Sample workplaces from map:");

  // Show first 5 workplaces as an example
  let count = 0;
  for (const [id, workplace] of workplacesMap) {
    if (count >= 5) break;
    console.log(`ID ${id}:`, workplace);
    count++;
  }
}

(async () => {
  await main();
})();
