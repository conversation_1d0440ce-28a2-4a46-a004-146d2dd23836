import { ShiftDTO } from "src/modules/shifts/shifts.schemas";
import { get } from "./get";

// Define the types based on the API structure
interface Workplace {
  id: number;
  name: string;
  status: number;
}

interface Shift {
  id: number;
  createdAt: string;
  startAt: string;
  endAt: string;
  workplaceId: number;
  workerId: number | null;
  cancelledAt: string | null;
}

interface PaginatedResponse<T> {
  data: T[];
  links: { next: string | null };
}

async function getAllWorkplaces(): Promise<Map<number, Workplace>> {
  const workplacesMap = new Map<number, Workplace>();
  let nextUrl: string | null = "http://localhost:3000/workplaces?page=1";

  while (nextUrl) {
    const response: PaginatedResponse<Workplace> = await get(nextUrl);

    // Add each workplace to the map with ID as key
    response.data.forEach((workplace) => {
      workplacesMap.set(workplace.id, workplace);
    });

    nextUrl = response.links.next;
  }

  return workplacesMap;
}

async function getAllShifts(): Promise<ShiftDTO[]> {
  const shifts: ShiftDTO[] = [];
  let nextUrl: string | null = "http://localhost:3000/shifts?page=1";

  while (nextUrl) {
    const response: PaginatedResponse<ShiftDTO> = await get(nextUrl);

    shifts.push(...response.data);

    nextUrl = response.links.next;
  }

  return shifts;
}

export async function main() {
  const workplacesMap = await getAllWorkplaces();
  const shifts = await getAllShifts();

  // Count shifts per workplace
  const shiftCounts = new Map<number, number>();

  shifts.forEach((shift) => {
    const workplaceId = shift.workplaceId;
    const currentCount = shiftCounts.get(workplaceId) || 0;
    shiftCounts.set(workplaceId, currentCount + 1);
  });

  // Create array of workplace names with shift counts
  const workplaceShiftCounts: { name: string; shifts: number }[] = [];

  for (const [workplaceId, shiftCount] of shiftCounts) {
    const workplace = workplacesMap.get(workplaceId);
    if (workplace) {
      workplaceShiftCounts.push({
        name: workplace.name,
        shifts: shiftCount,
      });
    }
  }

  // Sort by shift count (descending) and get top 3
  const top3Workplaces = workplaceShiftCounts.sort((a, b) => b.shifts - a.shifts).slice(0, 3);

  console.log("[");
  top3Workplaces.forEach((workplace, index) => {
    const comma = index < top3Workplaces.length - 1 ? "," : "";
    console.log(`  { "name": ${workplace.name}, "shifts": ${workplace.shifts} }${comma} `);
  });
  console.log("]");
}

(async () => {
  await main();
})();
