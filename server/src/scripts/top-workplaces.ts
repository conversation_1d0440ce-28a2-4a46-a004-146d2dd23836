import { get } from "./get";
import { ShiftDTO } from "src/modules/shifts/shifts.schemas";
import { PaginatedResponse } from "src/modules/shared/shared.types";
import { WorkplaceDTO } from "src/modules/workplaces/workplaces.schemas";

// Get base URL from environment variable or default to localhost
const BASE_URL = process.env.API_BASE_URL || "http://localhost:3000";

async function getAllWorkplaces(): Promise<Map<number, WorkplaceDTO>> {
  const workplacesMap = new Map<number, WorkplaceDTO>();
  let nextUrl: string | undefined = `${BASE_URL}/workplaces?page=1`;

  try {
    while (nextUrl) {
      const response: PaginatedResponse<WorkplaceDTO> = await get(nextUrl);

      if (!response || !Array.isArray(response.data)) {
        throw new Error(`Invalid response format from ${nextUrl}`);
      }

      // Add each workplace to the map with ID as key
      response.data.forEach((workplace) => {
        if (!workplace.id || !workplace.name) {
          console.warn(`Skipping invalid workplace:`, workplace);
          return;
        }
        workplacesMap.set(workplace.id, workplace);
      });

      nextUrl = response.links?.next;
    }

    return workplacesMap;
  } catch (error) {
    throw new Error(
      `Failed to fetch workplaces: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }
}

async function getAllShifts(): Promise<ShiftDTO[]> {
  const shifts: ShiftDTO[] = [];
  let nextUrl: string | undefined = `${BASE_URL}/shifts?page=1`;

  try {
    while (nextUrl) {
      const response: PaginatedResponse<ShiftDTO> = await get(nextUrl);

      if (!response || !Array.isArray(response.data)) {
        throw new Error(`Invalid response format from ${nextUrl}`);
      }

      // Validate shifts before adding them
      const validShifts = response.data.filter((shift) => {
        if (!shift.id || !shift.workplaceId) {
          console.warn(`Skipping invalid shift:`, shift);
          return false;
        }
        return true;
      });

      shifts.push(...validShifts);
      nextUrl = response.links?.next;
    }

    return shifts;
  } catch (error) {
    throw new Error(
      `Failed to fetch shifts: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }
}

export async function main() {
  try {
    const workplacesMap = await getAllWorkplaces();
    const shifts = await getAllShifts();

    if (workplacesMap.size === 0) {
      throw new Error("No workplaces found");
    }

    if (shifts.length === 0) {
      throw new Error("No shifts found");
    }

    // Count shifts per workplace
    const shiftCounts = new Map<number, number>();

    shifts.forEach((shift) => {
      const workplaceId = shift.workplaceId;
      const currentCount = shiftCounts.get(workplaceId) || 0;
      shiftCounts.set(workplaceId, currentCount + 1);
    });

    // Create array of workplace names with shift counts
    const workplaceShiftCounts: { name: string; shifts: number }[] = [];

    for (const [workplaceId, shiftCount] of shiftCounts) {
      const workplace = workplacesMap.get(workplaceId);
      if (workplace) {
        workplaceShiftCounts.push({
          name: workplace.name,
          shifts: shiftCount,
        });
      }
    }

    if (workplaceShiftCounts.length === 0) {
      throw new Error("No valid workplace-shift combinations found");
    }

    // Sort by shift count (descending) and get top 3
    const top3Workplaces = workplaceShiftCounts.sort((a, b) => b.shifts - a.shifts).slice(0, 3);

    console.log("[");
    top3Workplaces.forEach((workplace, index) => {
      const comma = index < top3Workplaces.length - 1 ? "," : "";
      console.log(`  { "name": "${workplace.name}", "shifts": ${workplace.shifts} }${comma}`);
    });
    console.log("]");
  } catch (error) {
    throw error;
  }
}

(async () => {
  try {
    await main();
  } catch (error) {
    console.error("Script failed:", error);
    process.exit(1);
  }
})();
